<?php

/**
 * The file that defines the core plugin class
 *
 * A class definition that includes attributes and functions used across both the
 * public-facing side of the site and the admin area.
 *
 * @since      1.0.0
 *
 * @package    USF_Auction
 * @subpackage USF_Auction/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 *
 * @since      1.0.0
 * @package    USF_Auction
 * @subpackage USF_Auction/includes
 */
class USF_Auction_Core {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      USF_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * Define the core functionality of the plugin.
     *
     * Set the plugin name and the plugin version that can be used throughout the plugin.
     * Load the dependencies, define the locale, and set the hooks for the admin area and
     * the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function __construct() {
        if (defined('USF_AUCTION_VERSION')) {
            $this->version = USF_AUCTION_VERSION;
        } else {
            $this->version = '1.0.0';
        }
        $this->plugin_name = 'usf-auction';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_public_hooks();
        $this->init_core_functionality();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * Include the following files that make up the plugin:
     *
     * - USF_Loader. Orchestrates the hooks of the plugin.
     * - USF_i18n. Defines internationalization functionality.
     * - USF_Admin. Defines all hooks for the admin area.
     * - USF_Public. Defines all hooks for the public side of the site.
     *
     * Create an instance of the loader which will be used to register the hooks
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies() {

        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-loader.php';

        /**
         * The class responsible for defining internationalization functionality
         * of the plugin.
         */
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-i18n.php';

        /**
         * Core functionality classes
         */
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-database.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-statistics-helper.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-csv-parser.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-auction-manager.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-bid-manager.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-email-manager.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-woocommerce.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-shortcodes.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-ajax-handlers.php';
        require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-auction-house-helper.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once USF_AUCTION_PLUGIN_DIR . 'admin/class-usf-auction-admin.php';

        /**
         * The class responsible for defining all actions that occur in the public-facing
         * side of the site.
         */
        require_once USF_AUCTION_PLUGIN_DIR . 'public/class-usf-auction-public.php';

        $this->loader = new USF_Loader();
    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * Uses the USF_i18n class in order to set the domain and to register the hook
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function set_locale() {
        $plugin_i18n = new USF_i18n();
        $this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_admin_hooks() {
        $plugin_admin = new USF_Auction_Admin($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');
        $this->loader->add_action('admin_menu', $plugin_admin, 'add_admin_menu');
        $this->loader->add_action('admin_init', $plugin_admin, 'admin_init');

        // WooCommerce integration hooks
        if (class_exists('WooCommerce')) {
            $this->loader->add_action('woocommerce_order_status_changed', 'USF_WooCommerce', 'handle_order_status_change', 10, 3);
            // HPOS compatible meta box registration
            $this->loader->add_action('add_meta_boxes_shop_order', 'USF_WooCommerce', 'add_order_meta_box');
            $this->loader->add_action('add_meta_boxes_woocommerce_page_wc-orders', 'USF_WooCommerce', 'add_order_meta_box');
        }
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_public_hooks() {
        $plugin_public = new USF_Auction_Public($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');

        // Initialize shortcodes
        $this->loader->add_action('init', $this, 'init_shortcodes');

        // Initialize AJAX handlers
        $this->loader->add_action('init', $this, 'init_ajax_handlers');

        // Add query vars for single auction pages
        $this->loader->add_filter('query_vars', $this, 'add_query_vars');
    }

    /**
     * Initialize core functionality
     *
     * @since    1.0.0
     * @access   private
     */
    private function init_core_functionality() {
        // Schedule cron events
        $this->loader->add_action('init', $this, 'schedule_cron_events');
        
        // Add cron hook for closing expired auctions
        $this->loader->add_action('usf_close_expired_auctions', 'USF_Auction_Manager', 'close_expired_auctions');

        // Create auction category on init
        if (class_exists('WooCommerce')) {
            $this->loader->add_action('init', 'USF_WooCommerce', 'create_auction_category');
        }
    }

    /**
     * Add custom query vars
     *
     * @since    1.0.0
     */
    public function add_query_vars($vars) {
        $vars[] = 'lot_id';
        return $vars;
    }

    /**
     * Schedule cron events
     *
     * @since    1.0.0
     */
    public function schedule_cron_events() {
        if (!wp_next_scheduled('usf_close_expired_auctions')) {
            wp_schedule_event(time(), 'hourly', 'usf_close_expired_auctions');
        }
    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.0.0
     */
    public function run() {
        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.0.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     1.0.0
     * @return    USF_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.0.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }

    /**
     * Initialize shortcodes
     *
     * @since    1.0.0
     */
    public function init_shortcodes() {
        USF_Shortcodes::init();
    }

    /**
     * Initialize AJAX handlers
     *
     * @since    1.0.0
     */
    public function init_ajax_handlers() {
        USF_Ajax_Handlers::init();
    }
}
